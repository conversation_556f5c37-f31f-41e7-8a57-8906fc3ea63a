import { But<PERSON>, Container } from "@/components";
import { Colors } from "@/constants/Colors";
import { Layout } from "@/constants/Layout";
import { Typography } from "@/constants/Typography";
import { useColorScheme } from "@/hooks/useColorScheme";
import { Item } from "@/types";
import { StyleSheetCreate, getItemById } from "@/utils";
import { showAlert } from "@/utils/alert";
import { deleteItem } from "@/utils/storage";
import { Ionicons } from "@expo/vector-icons";
import { Image } from "expo-image";
import { router, useFocusEffect, useLocalSearchParams } from "expo-router";
import React, { useCallback, useState } from "react";
import {
  NativeScrollEvent,
  NativeSyntheticEvent,
  Pressable,
  ScrollView,
  Text,
  View,
} from "react-native";

export default function ItemDetailsScreen() {
  const { id } = useLocalSearchParams();
  const colorScheme = useColorScheme();
  const colors = Colors[colorScheme ?? "light"];

  // State for real item data
  const [item, setItem] = useState<Item | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Load item data on component mount
  useFocusEffect(
    useCallback(() => {
      loadItem();
    }, [id])
  );

  const loadItem = async () => {
    if (!id || typeof id !== "string") {
      setError("Invalid item ID");
      setIsLoading(false);
      return;
    }

    try {
      setIsLoading(true);
      const response = await getItemById(id);

      if (response.success && response.data) {
        setItem(response.data);
      } else {
        setError("Item not found");
      }
    } catch (error) {
      console.error("Error loading item:", error);
      setError("Failed to load item");
    } finally {
      setIsLoading(false);
    }
  };

  // Image viewing state
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const [showFullscreenImage, setShowFullscreenImage] = useState(false);

  // Remove edit mode state since we navigate to creation page

  const handleBack = () => {
    router.back();
  };

  const handleEdit = () => {
    if (!item) return;

    // Navigate to item creation page with pre-filled data
    router.push({
      pathname: "/item/create",
      params: { id: item.id },
    });
  };

  const handleDelete = () => {
    if (!item || !item.id) return;

    showAlert(
      "删除故事",
      "确定要删除这个故事吗？此操作无法撤销。",
      [
        { text: "取消", style: "cancel" },
        {
          text: "删除",
          style: "destructive",
          onPress: async () => {
            try {
              const result = await deleteItem(item.id!);
              if (result.success) {
                showAlert(
                  "删除成功",
                  "故事已删除",
                  [
                    {
                      text: "确定",
                      onPress: () => router.back(),
                    },
                  ],
                  { icon: "checkmark-circle" }
                );
              } else {
                showAlert("删除失败", result.error || "删除故事时出现错误", undefined, {
                  icon: "alert-circle",
                });
              }
            } catch (error) {
              console.error("Error deleting item:", error);
              showAlert("删除失败", "删除故事时出现错误", undefined, { icon: "alert-circle" });
            }
          },
        },
      ],
      { icon: "trash" }
    );
  };

  const handleImagePress = (index: number) => {
    setCurrentImageIndex(index);
    setShowFullscreenImage(true);
  };



  const handleImageScroll = (event: NativeSyntheticEvent<NativeScrollEvent>) => {
    const index = Math.round(
      event.nativeEvent.contentOffset.x / event.nativeEvent.layoutMeasurement.width
    );
    setCurrentImageIndex(index);
  };

  // Show loading state
  if (isLoading) {
    return (
      <Container
        style={{ flex: 1, backgroundColor: colors.backgroundApp }}
        headerProps={{
          title: "故事详情",
        }}
      >
        <View style={[styles.content, styles.centerContent]}>
          <Text style={[styles.loadingText, { color: colors.textSecondary }]}>加载中...</Text>
        </View>
      </Container>
    );
  }

  // Show error state
  if (error || !item) {
    return (
      <Container
        style={{ flex: 1, backgroundColor: colors.backgroundApp }}
        headerProps={{
          title: "故事详情",
        }}
      >
        <View style={[styles.content, styles.centerContent]}>
          <Text style={[styles.errorText, { color: colors.error }]}>{error || "物品未找到"}</Text>
          <Button title="返回" onPress={handleBack} style={styles.backButton} />
        </View>
      </Container>
    );
  }

  return (
    <>
      <Container
        style={{ flex: 1, backgroundColor: colors.backgroundApp }}
        headerProps={{
          title: "故事详情",
          rightActions: [
            { icon: "trash", onPress: handleDelete },
            { icon: "create", onPress: handleEdit },
          ],
        }}
        enableScroll
      >
        <View style={styles.content}>
          {/* Item Header */}
          <View style={[styles.headerSection, { backgroundColor: colors.background }]}>
            <Text style={[styles.itemTitle, { color: colors.text }]}>{item.name}</Text>

            {/* Location and Time Info */}
            {(item.currentLocation || item.timeOfPossession) && (
              <View style={styles.infoContainer}>
                {item.currentLocation && (
                  <View style={styles.infoItem}>
                    <Ionicons name="location-outline" size={16} color={colors.textMuted} />
                    <Text style={[styles.infoText, { color: colors.textMuted }]}>
                      {item.currentLocation}
                    </Text>
                  </View>
                )}
                {item.timeOfPossession && (
                  <View style={styles.infoItem}>
                    <Ionicons name="calendar-outline" size={16} color={colors.textMuted} />
                    <Text style={[styles.infoText, { color: colors.textMuted }]}>
                      {item.timeOfPossession}
                    </Text>
                  </View>
                )}
              </View>
            )}

            {/* Tags */}
            {item.tags && item.tags.length > 0 && (
              <View style={styles.tagsContainer}>
                {item.tags.map((tag, index) => (
                  <View key={index} style={[styles.tag, { backgroundColor: colors.backgroundTertiary }]}>
                    <Text style={[styles.tagText, { color: colors.primary }]}>{tag}</Text>
                  </View>
                ))}
              </View>
            )}
          </View>

          {/* Content Images Gallery */}
          {!!item.images?.length && (
            <View style={[styles.contentSection, { backgroundColor: colors.background }]}>
              <View style={styles.sectionHeader}>
                <Text style={[styles.sectionTitle, { color: colors.text }]}>故事图片</Text>
                <Text style={[styles.imageCount, { color: colors.textMuted }]}>
                  {item.images?.length || 0}张图片
                </Text>
              </View>

              <View style={styles.imageGallery}>
                <ScrollView
                  horizontal
                  pagingEnabled
                  showsHorizontalScrollIndicator={false}
                  onMomentumScrollEnd={handleImageScroll}
                  style={styles.imageScrollView}
                >
                  {(item.images || []).map((imageUrl: string, index: number) => (
                    <Pressable
                      key={index}
                      style={styles.imageSlide}
                      onPress={() => handleImagePress(index)}
                    >
                      <Image source={{ uri: imageUrl }} style={styles.contentImage} />
                    </Pressable>
                  ))}
                </ScrollView>

                {/* Image Indicators */}
                <View style={styles.imageIndicators}>
                  {(item.images || []).map((_: string, index: number) => (
                    <View
                      key={index}
                      style={[
                        styles.indicator,
                        {
                          backgroundColor:
                            index === currentImageIndex ? colors.primary : colors.border,
                          opacity: index === currentImageIndex ? 1 : 0.5,
                        },
                      ]}
                    />
                  ))}
                </View>

                {/* Fullscreen Button */}
                <Pressable
                  style={styles.fullscreenButton}
                  onPress={() => handleImagePress(currentImageIndex)}
                >
                  <Ionicons name="expand" size={16} color="white" />
                </Pressable>
              </View>
            </View>
          )}

          {/* Item Content */}
          <View style={[styles.contentSection, { backgroundColor: colors.background }]}>
            <Text style={[styles.sectionTitle, { color: colors.text }]}>物品故事</Text>
            <View style={[styles.itemContent, { backgroundColor: colors.background }]}>
              <Text style={[styles.itemText, { color: colors.text }]}>{item.content}</Text>
            </View>
          </View>
        </View>
      </Container>

      {/* Fullscreen Image Modal */}
      {React.createElement(require("@/components/FullscreenImageModal").default, {
        visible: showFullscreenImage,
        images: item?.images || [],
        currentIndex: currentImageIndex,
        onClose: () => setShowFullscreenImage(false),
        onIndexChange: setCurrentImageIndex,
      })}


    </>
  );
}

const styles = StyleSheetCreate({
  content: {
    flex: 1,
  },

  // Header Section
  headerSection: {
    marginHorizontal: Layout.spacing.base,
    marginBottom: Layout.spacing.base,
    padding: Layout.spacing.lg,
    borderRadius: Layout.borderRadius.lg,
  },
  itemTitle: {
    fontSize: Typography.fontSize["2xl"],
    fontWeight: Typography.fontWeight.bold,
    marginBottom: Layout.spacing.base,
  },
  infoContainer: {
    flexDirection: "row",
    flexWrap: "wrap",
    gap: Layout.spacing.base,
    marginBottom: Layout.spacing.base,
  },
  infoItem: {
    flexDirection: "row",
    alignItems: "center",
    gap: Layout.spacing.xs,
  },
  infoText: {
    fontSize: Typography.fontSize.sm,
  },
  tagsContainer: {
    flexDirection: "row",
    flexWrap: "wrap",
    gap: Layout.spacing.xs,
  },
  tag: {
    paddingHorizontal: Layout.spacing.sm,
    paddingVertical: Layout.spacing.xs,
    borderRadius: Layout.borderRadius.md,
  },
  tagText: {
    fontSize: Typography.fontSize.sm,
    fontWeight: Typography.fontWeight.medium,
  },

  // Content Sections
  contentSection: {
    marginHorizontal: Layout.spacing.base,
    marginBottom: Layout.spacing.sm,
    paddingHorizontal: Layout.spacing.base,
    paddingVertical: Layout.spacing.lg,
    borderRadius: Layout.borderRadius.lg,
  },
  sectionHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: Layout.spacing.base,
  },
  sectionTitle: {
    fontSize: Typography.fontSize.lg,
    fontWeight: Typography.fontWeight.bold,
  },
  imageCount: {
    fontSize: Typography.fontSize.sm,
  },

  // Image Gallery
  imageGallery: {
    position: "relative",
  },
  imageScrollView: {
    borderRadius: Layout.borderRadius.lg,
    overflow: "hidden",
  },
  imageSlide: {
    width: 350, // Approximate screen width minus padding
    height: 200,
    marginRight: Layout.spacing.sm,
  },
  contentImage: {
    width: "100%",
    height: "100%",
    borderRadius: Layout.borderRadius.lg,
  },
  imageIndicators: {
    position: "absolute",
    bottom: 15,
    left: 0,
    right: 0,
    flexDirection: "row",
    justifyContent: "center",
    gap: 6,
  },
  indicator: {
    width: 8,
    height: 8,
    borderRadius: 4,
  },
  fullscreenButton: {
    position: "absolute",
    bottom: 15,
    right: 15,
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: "rgba(0, 0, 0, 0.6)",
    justifyContent: "center",
    alignItems: "center",
  },

  // item Content
  itemContent: {
    borderRadius: Layout.borderRadius.lg,
    padding: Layout.spacing.lg,
    marginTop: Layout.spacing.sm,
  },
  itemText: {
    fontSize: Typography.fontSize.base,
    lineHeight: 24,
  },



  // Action Section
  actionSection: {
    marginHorizontal: Layout.spacing.base,
    paddingHorizontal: Layout.spacing.base,
    paddingVertical: Layout.spacing.lg,
    marginBottom: Layout.spacing.xl,
    borderRadius: Layout.borderRadius.lg,
  },
  shareButton: {
    borderRadius: Layout.borderRadius.xl,
    paddingVertical: Layout.spacing.base,
  },

  // Missing styles
  section: {
    marginBottom: Layout.spacing.base,
  },
  // Loading and error states
  centerContent: {
    justifyContent: "center",
    alignItems: "center",
    paddingVertical: Layout.spacing["4xl"],
  },
  loadingText: {
    fontSize: Typography.fontSize.base,
    textAlign: "center",
  },
  errorText: {
    fontSize: Typography.fontSize.base,
    textAlign: "center",
    marginBottom: Layout.spacing.lg,
  },
  backButton: {
    alignSelf: "center",
  },
});
