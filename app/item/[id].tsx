import { <PERSON><PERSON>, Container } from "@/components";
import { Colors } from "@/constants/Colors";
import { useColorScheme } from "@/hooks/useColorScheme";
import { Item } from "@/types";
import { StyleSheetCreate, getItemById } from "@/utils";
import { showAlert } from "@/utils/alert";
import { deleteItem } from "@/utils/storage";
import { Ionicons } from "@expo/vector-icons";
import { Image } from "expo-image";
import { LinearGradient } from "expo-linear-gradient";
import { router, useFocusEffect, useLocalSearchParams } from "expo-router";
import React, { useCallback, useState } from "react";
import { Pressable, Text, View } from "react-native";

export default function ItemDetailsScreen() {
  const { id } = useLocalSearchParams();
  const colorScheme = useColorScheme();
  const colors = Colors[colorScheme ?? "light"];

  // State for real item data
  const [item, setItem] = useState<Item | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Load item data on component mount
  useFocusEffect(
    useCallback(() => {
      loadItem();
    }, [id])
  );

  const loadItem = async () => {
    if (!id || typeof id !== "string") {
      setError("Invalid item ID");
      setIsLoading(false);
      return;
    }

    try {
      setIsLoading(true);
      const response = await getItemById(id);

      if (response.success && response.data) {
        setItem(response.data);
      } else {
        setError("Item not found");
      }
    } catch (error) {
      console.error("Error loading item:", error);
      setError("Failed to load item");
    } finally {
      setIsLoading(false);
    }
  };

  // Image viewing state
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const [showFullscreenImage, setShowFullscreenImage] = useState(false);

  // Remove edit mode state since we navigate to creation page

  const handleBack = () => {
    router.back();
  };

  const handleEdit = () => {
    if (!item) return;

    // Navigate to item creation page with pre-filled data
    router.push({
      pathname: "/item/create",
      params: { id: item.id },
    });
  };

  const handleDelete = () => {
    if (!item || !item.id) return;

    showAlert(
      "删除故事",
      "确定要删除这个故事吗？此操作无法撤销。",
      [
        { text: "取消", style: "cancel" },
        {
          text: "删除",
          style: "destructive",
          onPress: async () => {
            try {
              const result = await deleteItem(item.id!);
              if (result.success) {
                showAlert(
                  "删除成功",
                  "故事已删除",
                  [
                    {
                      text: "确定",
                      onPress: () => router.back(),
                    },
                  ],
                  { icon: "checkmark-circle" }
                );
              } else {
                showAlert("删除失败", result.error || "删除故事时出现错误", undefined, {
                  icon: "alert-circle",
                });
              }
            } catch (error) {
              console.error("Error deleting item:", error);
              showAlert("删除失败", "删除故事时出现错误", undefined, { icon: "alert-circle" });
            }
          },
        },
      ],
      { icon: "trash" }
    );
  };

  const handleImagePress = (index: number) => {
    setCurrentImageIndex(index);
    setShowFullscreenImage(true);
  };

  // Show loading state
  if (isLoading) {
    return (
      <Container
        style={{ flex: 1, backgroundColor: colors.backgroundApp }}
        headerProps={{
          title: "故事详情",
        }}
      >
        <View style={[styles.content, styles.centerContent]}>
          <Text style={[styles.loadingText, { color: colors.textSecondary }]}>加载中...</Text>
        </View>
      </Container>
    );
  }

  // Show error state
  if (error || !item) {
    return (
      <Container
        style={{ flex: 1, backgroundColor: colors.backgroundApp }}
        headerProps={{
          title: "故事详情",
        }}
      >
        <View style={[styles.content, styles.centerContent]}>
          <Text style={[styles.errorText, { color: colors.error }]}>{error || "物品未找到"}</Text>
          <Button title="返回" onPress={handleBack} style={styles.backButton} />
        </View>
      </Container>
    );
  }

  return (
    <>
      <View style={styles.phoneContainer}>
        {/* Gradient Header */}
        <LinearGradient colors={["#58aee5", "#4a9fd9", "#3b8fcc"]} style={styles.gradientHeader}>
          {/* Header */}
          <View style={styles.header}>
            <Pressable style={styles.backBtn} onPress={handleBack}>
              <Ionicons name="chevron-back" size={22} color="white" />
            </Pressable>
            <Text style={styles.logo}>故事详情</Text>
            <View style={styles.headerIcons}>
              <Pressable style={styles.headerIcon} onPress={handleDelete}>
                <Ionicons name="trash" size={18} color="white" />
              </Pressable>
              <Pressable style={styles.headerIcon} onPress={handleEdit}>
                <Ionicons name="create" size={18} color="white" />
              </Pressable>
            </View>
          </View>

          {/* Product Image Container */}
          <View style={styles.productImageContainer}>
            <View style={styles.imageCarousel}>
              <View style={styles.carouselContainer}>
                {item.images && item.images.length > 0 ? (
                  <>
                    {/* Main Image */}
                    <Pressable
                      style={[styles.productImage, styles.mainImage]}
                      onPress={() => handleImagePress(currentImageIndex)}
                    >
                      <Image
                        source={{ uri: item.images[currentImageIndex] }}
                        style={styles.productImageContent}
                      />
                      <View style={styles.productLabel}>
                        <Text style={styles.productLabelIcon}>📖</Text>
                        <Text style={styles.productLabelText}>Story</Text>
                        <Text style={styles.productLabelSubtext}>by {item.name}</Text>
                      </View>
                    </Pressable>

                    {/* Side Images */}
                    {item.images.length > 1 && (
                      <>
                        <Pressable
                          style={[styles.productImage, styles.sideImage, styles.leftImage]}
                          onPress={() => {
                            const prevIndex =
                              currentImageIndex > 0
                                ? currentImageIndex - 1
                                : item.images!.length - 1;
                            setCurrentImageIndex(prevIndex);
                          }}
                        >
                          <Image
                            source={{
                              uri: item.images[
                                currentImageIndex > 0
                                  ? currentImageIndex - 1
                                  : item.images.length - 1
                              ],
                            }}
                            style={styles.productImageContent}
                          />
                        </Pressable>

                        {item.images.length > 2 && (
                          <Pressable
                            style={[styles.productImage, styles.sideImage, styles.rightImage]}
                            onPress={() => {
                              const nextIndex =
                                currentImageIndex < item.images!.length - 1
                                  ? currentImageIndex + 1
                                  : 0;
                              setCurrentImageIndex(nextIndex);
                            }}
                          >
                            <Image
                              source={{
                                uri: item.images[
                                  currentImageIndex < item.images.length - 1
                                    ? currentImageIndex + 1
                                    : 0
                                ],
                              }}
                              style={styles.productImageContent}
                            />
                          </Pressable>
                        )}
                      </>
                    )}

                    {/* Carousel Navigation */}
                    {item.images.length > 1 && (
                      <>
                        <Pressable
                          style={[styles.carouselNav, styles.prevNav]}
                          onPress={() => {
                            const prevIndex =
                              currentImageIndex > 0
                                ? currentImageIndex - 1
                                : item.images!.length - 1;
                            setCurrentImageIndex(prevIndex);
                          }}
                        >
                          <Ionicons name="chevron-back" size={20} color="#58aee5" />
                        </Pressable>
                        <Pressable
                          style={[styles.carouselNav, styles.nextNav]}
                          onPress={() => {
                            const nextIndex =
                              currentImageIndex < item.images!.length - 1
                                ? currentImageIndex + 1
                                : 0;
                            setCurrentImageIndex(nextIndex);
                          }}
                        >
                          <Ionicons name="chevron-forward" size={20} color="#58aee5" />
                        </Pressable>
                      </>
                    )}

                    {/* Carousel Indicators */}
                    {item.images.length > 1 && (
                      <View style={styles.carouselIndicators}>
                        {item.images.map((_, index) => (
                          <Pressable
                            key={index}
                            style={[
                              styles.indicator,
                              {
                                backgroundColor:
                                  index === currentImageIndex ? "white" : "rgba(255,255,255,0.4)",
                              },
                            ]}
                            onPress={() => setCurrentImageIndex(index)}
                          />
                        ))}
                      </View>
                    )}
                  </>
                ) : (
                  <View style={[styles.productImage, styles.mainImage, styles.placeholderImage]}>
                    <Ionicons name="image-outline" size={64} color="rgba(255,255,255,0.7)" />
                    <Text style={styles.placeholderText}>暂无图片</Text>
                  </View>
                )}
              </View>
            </View>
          </View>
        </LinearGradient>

        {/* Product Details Bottom Sheet */}
        <View style={styles.productDetails}>
          <Text style={styles.productTitle}>{item.name}</Text>

          {/* Time and Location Info */}
          {(item.timeOfPossession || item.currentLocation) && (
            <View style={styles.timeLocationSection}>
              {item.timeOfPossession && (
                <View style={styles.timeInfo}>
                  <Text style={styles.timeIcon}>⏰</Text>
                  <Text style={styles.timeText}>{item.timeOfPossession}</Text>
                </View>
              )}
              {item.currentLocation && (
                <View style={styles.locationInfo}>
                  <Text style={styles.locationIcon}>📍</Text>
                  <Text style={styles.locationText}>{item.currentLocation}</Text>
                </View>
              )}
            </View>
          )}

          {/* Tags Section */}
          {item.tags && item.tags.length > 0 && (
            <View style={styles.tagsSection}>
              <Text style={styles.tagsTitle}>标签</Text>
              <View style={styles.tagsContainer}>
                {item.tags.map((tag, index) => (
                  <View
                    key={index}
                    style={[
                      styles.tag,
                      index % 3 === 0
                        ? styles.tagPrimary
                        : index % 3 === 1
                        ? styles.tagSecondary
                        : styles.tagTertiary,
                    ]}
                  >
                    <Text style={styles.tagText}>{tag}</Text>
                  </View>
                ))}
              </View>
            </View>
          )}

          {/* Story Section */}
          <View style={styles.storySection}>
            <Text style={styles.storyTitle}>我的故事</Text>
            <View style={styles.storyContent}>
              <Text style={styles.storyText}>{item.content || "暂无故事内容"}</Text>
            </View>
          </View>
        </View>
      </View>

      {/* Fullscreen Image Modal */}
      {React.createElement(require("@/components/FullscreenImageModal").default, {
        visible: showFullscreenImage,
        images: item?.images || [],
        currentIndex: currentImageIndex,
        onClose: () => setShowFullscreenImage(false),
        onIndexChange: setCurrentImageIndex,
      })}
    </>
  );
}

const styles = StyleSheetCreate({
  phoneContainer: {
    flex: 1,
  },
  gradientHeader: {
    flex: 1,
  },
  header: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingHorizontal: 25,
    paddingTop: 60, // Account for status bar
    paddingBottom: 20,
  },
  backBtn: {
    width: 45,
    height: 45,
    backgroundColor: "rgba(255, 255, 255, 0.15)",
    borderRadius: 22.5,
    alignItems: "center",
    justifyContent: "center",
  },
  logo: {
    color: "white",
    fontSize: 26,
    fontWeight: "300",
    letterSpacing: 3,
    textShadowColor: "rgba(0, 0, 0, 0.1)",
    textShadowOffset: { width: 0, height: 2 },
    textShadowRadius: 4,
  },
  headerIcons: {
    flexDirection: "row",
    gap: 15,
  },
  headerIcon: {
    width: 45,
    height: 45,
    backgroundColor: "rgba(255, 255, 255, 0.15)",
    borderRadius: 22.5,
    alignItems: "center",
    justifyContent: "center",
  },
  // Product Image Container
  productImageContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    paddingHorizontal: 40,
    paddingVertical: 20,
  },
  imageCarousel: {
    position: "relative",
    width: "100%",
    height: 320,
    alignItems: "center",
    justifyContent: "center",
  },
  carouselContainer: {
    position: "relative",
    width: 280,
    height: 280,
    alignItems: "center",
    justifyContent: "center",
  },
  productImage: {
    position: "absolute",
    borderRadius: 25,
    backgroundColor: "rgba(255, 255, 255, 0.1)",
    shadowColor: "rgba(0, 0, 0, 0.2)",
    shadowOffset: { width: 0, height: 15 },
    shadowOpacity: 1,
    shadowRadius: 35,
    elevation: 8,
    overflow: "hidden",
  },
  mainImage: {
    width: 200,
    height: 280,
    zIndex: 3,
  },
  sideImage: {
    width: 160,
    height: 220,
    zIndex: 2,
    opacity: 0.7,
  },
  leftImage: {
    left: -80,
    transform: [{ rotateY: "-15deg" }],
  },
  rightImage: {
    right: -80,
    transform: [{ rotateY: "15deg" }],
  },
  placeholderImage: {
    alignItems: "center",
    justifyContent: "center",
    backgroundColor: "rgba(255, 255, 255, 0.2)",
  },
  placeholderText: {
    color: "rgba(255, 255, 255, 0.7)",
    fontSize: 16,
    marginTop: 8,
  },
  productImageContent: {
    width: "100%",
    height: "100%",
    borderRadius: 25,
  },
  productLabel: {
    position: "absolute",
    top: 10,
    left: 10,
    backgroundColor: "rgba(255, 255, 255, 0.8)",
    borderRadius: 10,
    padding: 8,
    shadowColor: "rgba(0, 0, 0, 0.1)",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 1,
    shadowRadius: 5,
    elevation: 2,
  },
  productLabelIcon: {
    fontSize: 16,
  },
  productLabelText: {
    fontSize: 14,
    fontWeight: "bold",
    color: "#333",
  },
  productLabelSubtext: {
    fontSize: 10,
    color: "#666",
  },
  // Carousel Navigation
  carouselNav: {
    position: "absolute",
    top: "50%",
    width: 45,
    height: 45,
    backgroundColor: "rgba(255, 255, 255, 0.9)",
    borderRadius: 22.5,
    alignItems: "center",
    justifyContent: "center",
    zIndex: 4,
    shadowColor: "rgba(0, 0, 0, 0.15)",
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 1,
    shadowRadius: 25,
    elevation: 4,
  },
  prevNav: {
    left: -20,
  },
  nextNav: {
    right: -20,
  },
  carouselIndicators: {
    position: "absolute",
    bottom: -30,
    left: "50%",
    transform: [{ translateX: -50 }],
    flexDirection: "row",
    gap: 8,
    zIndex: 4,
  },
  indicator: {
    width: 8,
    height: 8,
    borderRadius: 4,
  },
  // Product Details Bottom Sheet
  productDetails: {
    backgroundColor: "white",
    borderTopLeftRadius: 35,
    borderTopRightRadius: 35,
    paddingHorizontal: 25,
    paddingTop: 30,
    paddingBottom: 40,
    position: "absolute",
    bottom: 0,
    left: 0,
    right: 0,
    height: 350,
    shadowColor: "rgba(88, 174, 229, 0.1)",
    shadowOffset: { width: 0, height: -10 },
    shadowOpacity: 1,
    shadowRadius: 40,
    elevation: 8,
  },
  productTitle: {
    fontSize: 32,
    fontWeight: "800",
    color: "#1a365d",
    marginBottom: 20,
    textShadowColor: "rgba(0, 0, 0, 0.05)",
    textShadowOffset: { width: 0, height: 2 },
    textShadowRadius: 4,
    lineHeight: 38,
  },
  // Time and Location Section
  timeLocationSection: {
    flexDirection: "row",
    alignItems: "center",
    gap: 20,
    marginBottom: 20,
    paddingVertical: 15,
  },
  timeInfo: {
    flexDirection: "row",
    alignItems: "center",
    gap: 8,
    backgroundColor: "rgba(88, 174, 229, 0.1)",
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    borderWidth: 1,
    borderColor: "rgba(88, 174, 229, 0.2)",
  },
  timeIcon: {
    fontSize: 16,
  },
  timeText: {
    fontSize: 14,
    fontWeight: "600",
    color: "#58aee5",
  },
  locationInfo: {
    flexDirection: "row",
    alignItems: "center",
    gap: 8,
    backgroundColor: "rgba(16, 185, 129, 0.1)",
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    borderWidth: 1,
    borderColor: "rgba(16, 185, 129, 0.2)",
  },
  locationIcon: {
    fontSize: 16,
  },
  locationText: {
    fontSize: 14,
    fontWeight: "600",
    color: "#10b981",
  },
  // Tags Section
  tagsSection: {
    marginBottom: 25,
  },
  tagsTitle: {
    fontSize: 16,
    fontWeight: "600",
    color: "#4a5568",
    marginBottom: 12,
  },
  tagsContainer: {
    flexDirection: "row",
    flexWrap: "wrap",
    gap: 8,
  },
  tag: {
    paddingHorizontal: 14,
    paddingVertical: 6,
    borderRadius: 18,
    shadowColor: "rgba(88, 174, 229, 0.2)",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 1,
    shadowRadius: 8,
    elevation: 2,
  },
  tagPrimary: {
    backgroundColor: "#58aee5",
  },
  tagSecondary: {
    backgroundColor: "#f59e0b",
  },
  tagTertiary: {
    backgroundColor: "#10b981",
  },
  tagText: {
    color: "white",
    fontSize: 12,
    fontWeight: "600",
  },
  // Story Section
  storySection: {
    marginBottom: 25,
  },
  storyTitle: {
    fontSize: 18,
    fontWeight: "700",
    color: "#1a365d",
    marginBottom: 15,
    flexDirection: "row",
    alignItems: "center",
    gap: 8,
  },
  storyContent: {
    backgroundColor: "#f8fafc",
    borderRadius: 20,
    padding: 20,
    borderWidth: 1,
    borderColor: "rgba(88, 174, 229, 0.1)",
    shadowColor: "rgba(88, 174, 229, 0.05)",
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 1,
    shadowRadius: 15,
    elevation: 2,
    borderLeftWidth: 4,
    borderLeftColor: "#58aee5",
  },
  storyText: {
    fontSize: 15,
    lineHeight: 24,
    color: "#4a5568",
    textAlign: "justify",
  },
  // Legacy styles for compatibility
  content: {
    flex: 1,
  },
  centerContent: {
    justifyContent: "center",
    alignItems: "center",
    paddingVertical: 64,
  },
  loadingText: {
    fontSize: 16,
    textAlign: "center",
  },
  errorText: {
    fontSize: 16,
    textAlign: "center",
    marginBottom: 20,
  },
  backButton: {
    alignSelf: "center",
  },
});
