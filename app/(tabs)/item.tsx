import { Colors } from "@/constants/Colors";
import { Layout } from "@/constants/Layout";
import { useColorScheme } from "@/hooks/useColorScheme";
import { Item } from "@/types";
import { getAllStories, StyleSheetCreate } from "@/utils";
import { showAlert } from "@/utils/alert";
import { deleteItem } from "@/utils/storage";
import { Ionicons } from "@expo/vector-icons";
import { Image } from "expo-image";
import { LinearGradient } from "expo-linear-gradient";
import { router, useFocusEffect } from "expo-router";
import { useCallback, useState } from "react";
import { Pressable, RefreshControl, ScrollView, Text, View } from "react-native";

// Real item data is now loaded from local storage

export default function ObjectScreen() {
  const colorScheme = useColorScheme();
  const colors = Colors[colorScheme ?? "light"];

  // State for real item data
  const [stories, setStories] = useState<Item[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);

  // Edit mode state
  const [isEditMode, setIsEditMode] = useState(false);
  const [selectedStories, setSelectedStories] = useState<Set<string>>(new Set());

  // Load stories on component mount
  useFocusEffect(
    useCallback(() => {
      loadStories();
    }, [])
  );

  const loadStories = async () => {
    try {
      setIsLoading(true);
      const response = await getAllStories();
      if (response.success) {
        setStories(response.data || []);
      } else {
        console.error("Failed to load stories:", response.error);
      }
    } catch (error) {
      console.error("Error loading stories:", error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    await loadStories();
    setRefreshing(false);
  };



  const handleItemPress = (item: Item) => {
    if (isEditMode) {
      toggleItemSelection(item.id!);
    } else {
      router.push(`/item/${item.id}`);
    }
  };

  const handleEditMode = () => {
    setIsEditMode(!isEditMode);
    setSelectedStories(new Set());
  };

  const toggleItemSelection = (itemId: string) => {
    const newSelection = new Set(selectedStories);
    if (newSelection.has(itemId)) {
      newSelection.delete(itemId);
    } else {
      newSelection.add(itemId);
    }
    setSelectedStories(newSelection);
  };

  const handleDeleteSelected = () => {
    if (selectedStories.size === 0) return;

    const itemCount = selectedStories.size;
    const message =
      itemCount === 1 ? "确定要删除这个故事吗？" : `确定要删除这 ${itemCount} 个故事吗？`;

    showAlert(
      "删除故事",
      message,
      [
        { text: "取消", style: "cancel" },
        {
          text: "删除",
          style: "destructive",
          onPress: async () => {
            try {
              const deletePromises = Array.from(selectedStories).map(id => deleteItem(id));
              await Promise.all(deletePromises);
              await loadStories();
              setSelectedStories(new Set());
              setIsEditMode(false);
            } catch (error) {
              console.error("Error deleting stories:", error);
              showAlert("错误", "删除故事时出现错误", undefined, { icon: "alert-circle" });
            }
          },
        },
      ],
      { icon: "trash" }
    );
  };



  return (
    <View style={styles.phoneContainer}>
      {/* Gradient Background */}
      <LinearGradient
        colors={['#58aee5', '#4a9fd9', '#3b8fcc']}
        style={styles.gradientBackground}
      >
        {/* Header */}
        <View style={styles.header}>
          <Text style={styles.headerTitle}>Records</Text>
          <Pressable style={styles.addBtn} onPress={() => router.push("/item/create")}>
            <Ionicons name="add" size={20} color="white" />
          </Pressable>
        </View>

        {/* Content */}
        <ScrollView
          style={styles.content}
          showsVerticalScrollIndicator={false}
          refreshControl={
            <RefreshControl
              refreshing={refreshing}
              onRefresh={handleRefresh}
              tintColor="white"
            />
          }
        >
          {isLoading && stories.length === 0 ? (
            <View style={styles.emptyContainer}>
              <View style={styles.emptyIconContainer}>
                <Ionicons name="hourglass-outline" size={64} color="rgba(255,255,255,0.7)" />
              </View>
              <Text style={styles.emptyTitle}>加载中...</Text>
              <Text style={styles.emptySubText}>正在获取您的故事</Text>
            </View>
          ) : stories.length === 0 ? (
            <View style={styles.emptyContainer}>
              <View style={styles.emptyIconContainer}>
                <Ionicons name="library-outline" size={64} color="rgba(255,255,255,0.7)" />
              </View>
              <Text style={styles.emptyTitle}>故事集为空</Text>
              <Text style={styles.emptySubText}>
                您还没有创建任何故事{"\n"}
                开始记录生活中的美好瞬间吧
              </Text>
              <Pressable style={styles.createButton} onPress={() => router.push("/item/create")}>
                <Text style={styles.createButtonText}>创建第一个故事</Text>
              </Pressable>
            </View>
          ) : (
            <>
              {/* Today's Pick */}
              {stories.length > 0 && (
                <View style={styles.todaysPick}>
                  <View style={styles.pickHeader}>
                    <Text style={styles.pickIcon}>✨</Text>
                    <Text style={styles.pickLabel}>Today's Pick</Text>
                  </View>
                  <Pressable
                    style={styles.pickContent}
                    onPress={() => handleItemPress(stories[0])}
                  >
                    <View style={styles.pickInfo}>
                      <Text style={styles.pickTitle}>{stories[0].name}</Text>
                    </View>
                    <View style={styles.pickImage}>
                      {stories[0].images && stories[0].images.length > 0 ? (
                        <Image source={{ uri: stories[0].images[0] }} style={styles.pickImageContent} />
                      ) : (
                        <Text style={styles.pickImagePlaceholder}>🎵</Text>
                      )}
                    </View>
                  </Pressable>
                </View>
              )}

              {/* Record Groups */}
              <View style={styles.section}>
                <View style={styles.sectionHeader}>
                  <Text style={styles.sectionTitle}>Story Groups</Text>
                  <Pressable onPress={handleEditMode}>
                    <Ionicons name="chevron-forward" size={18} color="rgba(255,255,255,0.7)" />
                  </Pressable>
                </View>

                {stories.map((story, index) => (
                  <Pressable
                    key={story.id || index}
                    style={styles.recordGroup}
                    onPress={() => handleItemPress(story)}
                  >
                    <View style={styles.groupContent}>
                      <Text style={styles.groupName}>{story.name}</Text>
                      <Text style={styles.groupDescription} numberOfLines={2}>
                        {story.content || "暂无内容"}
                      </Text>
                    </View>
                    <Ionicons name="chevron-forward" size={16} color="#9ca3af" />
                    {isEditMode && (
                      <Pressable
                        style={[
                          styles.selectionButton,
                          { backgroundColor: selectedStories.has(story.id!) ? colors.primary : 'rgba(255,255,255,0.3)' },
                        ]}
                        onPress={() => toggleItemSelection(story.id!)}
                      >
                        {selectedStories.has(story.id!) && <Ionicons name="checkmark" size={16} color="white" />}
                      </Pressable>
                    )}
                  </Pressable>
                ))}
              </View>
            </>
          )}
        </ScrollView>

        {/* Edit Mode Actions */}
        {isEditMode && (
          <View style={styles.editModeActions}>
            <Pressable style={styles.editActionButton} onPress={handleDeleteSelected}>
              <Ionicons name="trash" size={20} color="white" />
              <Text style={styles.editActionText}>删除</Text>
            </Pressable>
            <Pressable style={styles.editActionButton} onPress={handleEditMode}>
              <Ionicons name="close" size={20} color="white" />
              <Text style={styles.editActionText}>取消</Text>
            </Pressable>
          </View>
        )}
      </LinearGradient>
    </View>
  );
}

const styles = StyleSheetCreate({
  phoneContainer: {
    flex: 1,
    backgroundColor: '#58aee5',
  },
  gradientBackground: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 25,
    paddingTop: 60, // Account for status bar
    paddingBottom: 20,
  },
  headerTitle: {
    fontSize: 34,
    fontWeight: '800',
    color: 'white',
    textShadowColor: 'rgba(0, 0, 0, 0.1)',
    textShadowOffset: { width: 0, height: 2 },
    textShadowRadius: 4,
  },
  addBtn: {
    width: 35,
    height: 35,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    borderRadius: 17.5,
    alignItems: 'center',
    justifyContent: 'center',
    shadowColor: 'rgba(255, 255, 255, 0.3)',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 1,
    shadowRadius: 15,
    elevation: 4,
  },
  content: {
    flex: 1,
    paddingHorizontal: 25,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    paddingHorizontal: Layout.spacing.xl,
    paddingVertical: Layout.spacing["4xl"],
  },
  emptyIconContainer: {
    width: 120,
    height: 120,
    borderRadius: 60,
    alignItems: "center",
    justifyContent: "center",
    marginBottom: Layout.spacing.xl,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
  },
  emptyTitle: {
    fontSize: 24,
    fontWeight: "700",
    textAlign: "center",
    marginBottom: Layout.spacing.base,
    color: 'white',
  },
  emptySubText: {
    fontSize: 16,
    textAlign: "center",
    lineHeight: 24,
    marginBottom: Layout.spacing["2xl"],
    color: 'rgba(255, 255, 255, 0.8)',
  },
  createButton: {
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 20,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.3)',
  },
  createButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
  // Today's Pick styles
  todaysPick: {
    backgroundColor: 'rgba(255, 255, 255, 0.15)',
    borderRadius: 20,
    padding: 20,
    marginBottom: 30,
    shadowColor: 'rgba(0, 0, 0, 0.1)',
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 1,
    shadowRadius: 25,
    elevation: 4,
  },
  pickHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    marginBottom: 8,
  },
  pickIcon: {
    fontSize: 16,
  },
  pickLabel: {
    fontSize: 12,
    fontWeight: '700',
    color: 'white',
    textTransform: 'uppercase',
    letterSpacing: 1,
  },
  pickContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  pickInfo: {
    flex: 1,
  },
  pickTitle: {
    fontSize: 16,
    fontWeight: '700',
    color: 'white',
    lineHeight: 20,
  },
  pickImage: {
    width: 80,
    height: 80,
    borderRadius: 15,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    alignItems: 'center',
    justifyContent: 'center',
    shadowColor: 'rgba(0, 0, 0, 0.2)',
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 1,
    shadowRadius: 20,
    elevation: 4,
    overflow: 'hidden',
  },
  pickImageContent: {
    width: '100%',
    height: '100%',
    borderRadius: 15,
  },
  pickImagePlaceholder: {
    fontSize: 32,
    color: 'white',
  },
  // Section styles
  section: {
    marginBottom: 30,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 15,
  },
  sectionTitle: {
    fontSize: 22,
    fontWeight: '700',
    color: 'white',
  },
  // Record Group styles
  recordGroup: {
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
    borderRadius: 15,
    padding: 16,
    marginBottom: 12,
    flexDirection: 'row',
    alignItems: 'center',
    shadowColor: 'rgba(0, 0, 0, 0.1)',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 1,
    shadowRadius: 15,
    elevation: 2,
    position: 'relative',
  },
  groupContent: {
    flex: 1,
  },
  groupName: {
    fontSize: 17,
    fontWeight: '700',
    color: '#1a365d',
    marginBottom: 4,
  },
  groupDescription: {
    fontSize: 14,
    color: '#718096',
    lineHeight: 18,
  },
  // Selection and edit mode styles
  selectionButton: {
    position: "absolute",
    top: 12,
    right: 12,
    width: 28,
    height: 28,
    borderRadius: 14,
    alignItems: "center",
    justifyContent: "center",
    borderWidth: 2,
    borderColor: "rgba(255, 255, 255, 0.8)",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  editModeActions: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    paddingHorizontal: 25,
    paddingVertical: 20,
    backgroundColor: 'rgba(0, 0, 0, 0.1)',
  },
  editActionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    paddingHorizontal: 20,
    paddingVertical: 12,
    borderRadius: 20,
    gap: 8,
  },
  editActionText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
  // Legacy styles for compatibility
  itemCardContainer: {
    position: "relative",
    marginBottom: Layout.spacing.base,
  },
  itemCard: {
    marginBottom: Layout.spacing.base,
  },
});
