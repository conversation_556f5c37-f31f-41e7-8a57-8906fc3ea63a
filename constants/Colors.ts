/**
 * Design system colors for the Memory Museum (记忆博物馆)
 * Based on the third version prototypes with primary color #5D8BF4 (blue)
 */

// const primaryColor = "#5D8BF4";
const primaryColor = "#58aee5";
const primaryColorLight = "#7BA3F7";
const primaryColorDark = "#4A73E1";
const secondaryColor = "#FF7E5D";

export const Colors = {
  light: {
    // Primary brand colors
    primary: primaryColor,
    primaryLight: primaryColorLight,
    primaryDark: primaryColorDark,
    secondary: secondaryColor,

    // Background colors
    background: "#ffffff",
    backgroundSecondary: "#f5f5f7",
    backgroundTertiary: "#f3f4f6",
    backgroundApp: "#f1f5f9", // Light gray background for app

    // Text colors
    text: "#1f2937",
    textSecondary: "#6b7280",
    textMuted: "#9ca3af",
    textInverse: "#ffffff",

    // UI element colors
    border: "#e5e7eb",
    borderLight: "#f3f4f6",
    shadow: "rgba(0, 0, 0, 0.1)",
    shadowCard: "rgba(0, 0, 0, 0.05)",

    // Status colors
    success: "#10b981",
    warning: "#f59e0b",
    error: "#ef4444",
    info: primaryColor,

    // AI and feature colors
    aiBadge: "rgba(93, 139, 244, 0.1)",
    aiText: primaryColor,
    premium: "#ffd700",
    premiumGradient: ["#ffd700", "#ff7e5d"],

    // Story and content colors
    storyProgress: primaryColor,
    heartRed: "#ef4444",

    // Tab bar
    tabBackground: "rgba(255, 255, 255, 0.95)",
    tabIconDefault: "#9ca3af",
    tabIconSelected: primaryColor,

    // Legacy compatibility
    tint: primaryColor,
    icon: "#6b7280",
  },
  dark: {
    // Primary brand colors (same as light for consistency)
    primary: primaryColor,
    primaryLight: primaryColorLight,
    primaryDark: primaryColorDark,
    secondary: secondaryColor,

    // Background colors
    background: "#1f2937",
    backgroundSecondary: "#111827",
    backgroundTertiary: "#374151",
    backgroundApp: "#0f172a", // Dark background for app

    // Text colors
    text: "#f9fafb",
    textSecondary: "#d1d5db",
    textMuted: "#9ca3af",
    textInverse: "#1f2937",

    // UI element colors
    border: "#4b5563",
    borderLight: "#374151",
    shadow: "rgba(0, 0, 0, 0.3)",
    shadowCard: "rgba(0, 0, 0, 0.2)",

    // Status colors
    success: "#10b981",
    warning: "#f59e0b",
    error: "#ef4444",
    info: primaryColor,

    // AI and feature colors
    aiBadge: "rgba(93, 139, 244, 0.2)",
    aiText: primaryColorLight,
    premium: "#ffd700",
    premiumGradient: ["#ffd700", "#ff7e5d"],

    // Story and content colors
    storyProgress: primaryColor,
    heartRed: "#ef4444",

    // Tab bar
    tabBackground: "rgba(31, 41, 55, 0.95)",
    tabIconDefault: "#9ca3af",
    tabIconSelected: primaryColor,

    // Legacy compatibility
    tint: primaryColor,
    icon: "#d1d5db",
  },
};
