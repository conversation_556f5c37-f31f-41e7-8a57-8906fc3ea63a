<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Cypresses - Art Detail</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #000;
            color: #fff;
            overflow-x: hidden;
            min-height: 100vh;
        }

        .ios-container {
            max-width: 390px;
            margin: 0 auto;
            background-color: #000;
            min-height: 100vh;
            position: relative;
        }

        /* Status Bar */
        .status-bar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 20px;
            font-size: 14px;
            font-weight: 600;
            background-color: rgba(0, 0, 0, 0.1);
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            z-index: 10;
            color: #fff;
        }

        .status-left {
            display: flex;
            align-items: center;
            gap: 4px;
        }

        .signal-bars {
            display: flex;
            gap: 2px;
        }

        .bar {
            width: 3px;
            background-color: #fff;
            border-radius: 1px;
        }

        .bar:nth-child(1) { height: 4px; }
        .bar:nth-child(2) { height: 6px; }
        .bar:nth-child(3) { height: 8px; }
        .bar:nth-child(4) { height: 10px; }

        /* Main Content */
        .main-content {
            padding-top: 44px;
        }

        /* Hero Image Section */
        .hero-section {
            position: relative;
            height: 60vh;
            overflow: hidden;
        }

        .hero-image {
            width: 100%;
            height: 100%;
            object-fit: cover;
            object-position: center;
        }

        .hero-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(transparent 60%, rgba(0, 0, 0, 0.8));
        }

        .hero-controls {
            position: absolute;
            top: 20px;
            left: 20px;
            right: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .today-label {
            background-color: rgba(255, 255, 255, 0.9);
            color: #000;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 600;
            text-transform: uppercase;
        }

        .action-buttons {
            display: flex;
            gap: 16px;
        }

        .action-btn {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background-color: rgba(255, 255, 255, 0.9);
            border: none;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: transform 0.2s;
        }

        .action-btn:hover {
            transform: scale(1.1);
        }

        .action-btn svg {
            width: 18px;
            height: 18px;
            color: #000;
        }

        /* Detail Section */
        .detail-section {
            padding: 24px 20px;
            background-color: #000;
        }

        .artwork-title {
            font-family: 'Times New Roman', serif;
            font-size: 32px;
            font-weight: 400;
            font-style: italic;
            margin-bottom: 8px;
            line-height: 1.2;
        }

        .artwork-specs {
            color: #999;
            font-size: 14px;
            margin-bottom: 20px;
        }

        .artist-info {
            display: flex;
            align-items: center;
            gap: 12px;
            margin-bottom: 20px;
        }

        .artist-avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            object-fit: cover;
        }

        .artist-details h3 {
            font-size: 16px;
            font-weight: 500;
            margin-bottom: 2px;
        }

        .artist-year {
            color: #999;
            font-size: 14px;
        }

        .artwork-description {
            color: #ccc;
            font-size: 15px;
            line-height: 1.5;
            margin-bottom: 30px;
        }

        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 100%;
            max-width: 390px;
            background-color: #1c1c1e;
            padding: 8px 0 34px 0;
            border-top: 1px solid #333;
        }

        .nav-items {
            display: flex;
            justify-content: space-around;
            align-items: center;
        }

        .nav-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 4px;
            color: #999;
            text-decoration: none;
            font-size: 10px;
            transition: color 0.2s;
        }

        .nav-item.active {
            color: #007AFF;
        }

        .nav-item svg {
            width: 24px;
            height: 24px;
        }

        /* Responsive Design */
        @media (max-width: 375px) {
            .ios-container {
                max-width: 100%;
            }
            
            .artwork-title {
                font-size: 28px;
            }
            
            .detail-section {
                padding: 20px 16px;
            }
        }

        @media (min-width: 768px) {
            .ios-container {
                max-width: 390px;
                box-shadow: 0 0 20px rgba(0, 0, 0, 0.5);
            }
        }

        /* Accessibility */
        @media (prefers-reduced-motion: reduce) {
            .action-btn {
                transition: none;
            }
        }

        .sr-only {
            position: absolute;
            width: 1px;
            height: 1px;
            padding: 0;
            margin: -1px;
            overflow: hidden;
            clip: rect(0, 0, 0, 0);
            white-space: nowrap;
            border: 0;
        }
    </style>
</head>
<body>
    <div class="ios-container">
        <!-- Status Bar -->
        <header class="status-bar" role="banner">
            <div class="status-left">
                <div class="signal-bars" aria-label="Signal strength">
                    <div class="bar"></div>
                    <div class="bar"></div>
                    <div class="bar"></div>
                    <div class="bar"></div>
                </div>
                <svg width="15" height="11" viewBox="0 0 15 11" fill="currentColor">
                    <path d="M1 4h13v3H1V4zm1-2h11v1H2V2zm1-2h9v1H3V0z"/>
                </svg>
                <svg width="24" height="12" viewBox="0 0 24 12" fill="currentColor">
                    <path d="M2 2h20v8H2V2z"/>
                </svg>
            </div>
            <time>9:41</time>
            <div class="status-right">
                <svg width="24" height="12" viewBox="0 0 24 12" fill="currentColor">
                    <rect x="2" y="3" width="18" height="6" rx="2" fill="none" stroke="currentColor" stroke-width="1"/>
                    <rect x="21" y="5" width="2" height="2" rx="1"/>
                </svg>
            </div>
        </header>

        <main class="main-content">
            <!-- Hero Image Section -->
            <section class="hero-section">
                <img 
                    src="https://images.unsplash.com/photo-1503095396549-807759245b35" 
                    alt="Cypresses by Vincent van Gogh - Oil painting featuring dark cypress trees against a swirling sky"
                    class="hero-image"
                >
                <div class="hero-overlay"></div>
                <div class="hero-controls">
                    <span class="today-label">Today</span>
                    <div class="action-buttons">
                        <button class="action-btn" aria-label="Add to favorites">
                            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <path d="M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z"/>
                            </svg>
                        </button>
                        <button class="action-btn" aria-label="Share artwork">
                            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <path d="M4 12v8a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2v-8"/>
                                <polyline points="16,6 12,2 8,6"/>
                                <line x1="12" y1="2" x2="12" y2="15"/>
                            </svg>
                        </button>
                    </div>
                </div>
            </section>

            <!-- Detail Section -->
            <section class="detail-section">
                <header>
                    <h1 class="artwork-title">Cypresses</h1>
                    <p class="artwork-specs">Oil on canvas • 93.3 × 74 cm</p>
                </header>

                <div class="artist-info">
                    <img 
                        src="/placeholder.svg?height=32&width=32" 
                        alt="Vincent van Gogh"
                        class="artist-avatar"
                    >
                    <div class="artist-details">
                        <h2>Vincent van Gogh</h2>
                        <p class="artist-year">1889</p>
                    </div>
                </div>

                <div class="artwork-description">
                    <p>Cypresses was painted in late June 1889, shortly after Van Gogh began his year-long stay at the asylum in Saint-Rémy. The subject matter held deep significance for the artist, who wrote to his brother Theo about his fascination with these "beautiful lines and proportions like an Egyptian obelisk."</p>
                </div>
            </section>
        </main>

        <!-- Bottom Navigation -->
        <nav class="bottom-nav" role="navigation" aria-label="Main navigation">
            <div class="nav-items">
                <a href="#gallery" class="nav-item active" aria-current="page">
                    <svg viewBox="0 0 24 24" fill="currentColor">
                        <rect x="3" y="3" width="7" height="7"/>
                        <rect x="14" y="3" width="7" height="7"/>
                        <rect x="14" y="14" width="7" height="7"/>
                        <rect x="3" y="14" width="7" height="7"/>
                    </svg>
                    <span>Gallery</span>
                </a>
                <a href="#discover" class="nav-item">
                    <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <circle cx="12" cy="12" r="10"/>
                        <polygon points="16.24,7.76 14.12,14.12 7.76,16.24 9.88,9.88 16.24,7.76"/>
                    </svg>
                    <span>Discover</span>
                </a>
                <a href="#search" class="nav-item">
                    <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <circle cx="11" cy="11" r="8"/>
                        <path d="m21 21-4.35-4.35"/>
                    </svg>
                    <span>Search</span>
                </a>
                <a href="#favorites" class="nav-item">
                    <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <path d="M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z"/>
                    </svg>
                    <span>Favorites</span>
                </a>
                <a href="#settings" class="nav-item">
                    <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <circle cx="12" cy="12" r="3"/>
                        <path d="M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1 1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1z"/>
                    </svg>
                    <span>Settings</span>
                </a>
            </div>
        </nav>
    </div>

    <script>
        // Add interactive functionality
        document.addEventListener('DOMContentLoaded', function() {
            // Handle favorite button
            const favoriteBtn = document.querySelector('[aria-label="Add to favorites"]');
            let isFavorited = false;
            
            favoriteBtn.addEventListener('click', function() {
                isFavorited = !isFavorited;
                const heartPath = this.querySelector('path');
                
                if (isFavorited) {
                    heartPath.setAttribute('fill', '#ff3b30');
                    heartPath.setAttribute('stroke', '#ff3b30');
                    this.setAttribute('aria-label', 'Remove from favorites');
                } else {
                    heartPath.setAttribute('fill', 'none');
                    heartPath.setAttribute('stroke', 'currentColor');
                    this.setAttribute('aria-label', 'Add to favorites');
                }
            });

            // Handle share button
            const shareBtn = document.querySelector('[aria-label="Share artwork"]');
            shareBtn.addEventListener('click', function() {
                if (navigator.share) {
                    navigator.share({
                        title: 'Cypresses by Vincent van Gogh',
                        text: 'Check out this amazing artwork from 1889',
                        url: window.location.href
                    });
                } else {
                    // Fallback for browsers that don't support Web Share API
                    const url = window.location.href;
                    navigator.clipboard.writeText(url).then(() => {
                        alert('Link copied to clipboard!');
                    });
                }
            });

            // Handle navigation
            const navItems = document.querySelectorAll('.nav-item');
            navItems.forEach(item => {
                item.addEventListener('click', function(e) {
                    e.preventDefault();
                    
                    // Remove active class from all items
                    navItems.forEach(nav => {
                        nav.classList.remove('active');
                        nav.removeAttribute('aria-current');
                    });
                    
                    // Add active class to clicked item
                    this.classList.add('active');
                    this.setAttribute('aria-current', 'page');
                });
            });

            // Add smooth scrolling for better UX
            document.documentElement.style.scrollBehavior = 'smooth';
        });
    </script>
</body>
</html>
