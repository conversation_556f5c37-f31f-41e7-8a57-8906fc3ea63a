<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Records - Music App</title>
    <style>
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      body {
        font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
        background: linear-gradient(135deg, #58aee5 0%, #4a9fd9 50%, #3b8fcc 100%);
        min-height: 100vh;
        display: flex;
        justify-content: center;
        align-items: center;
        padding: 20px;
      }

      .phone-container {
        width: 375px;
        height: 812px;
        background: linear-gradient(180deg, #f8f9fa 0%, #ffffff 100%);
        border-radius: 40px;
        position: relative;
        box-shadow: 0 25px 50px rgba(88, 174, 229, 0.4);
        overflow: hidden;
        border: 1px solid rgba(255, 255, 255, 0.2);
      }

      /* Status Bar */
      .status-bar {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 15px 25px 10px;
        font-size: 17px;
        font-weight: 600;
        color: #1a365d;
      }

      .time {
        font-weight: 700;
      }

      .status-icons {
        display: flex;
        align-items: center;
        gap: 5px;
        font-size: 16px;
      }

      /* Header */
      .header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 10px 25px 20px;
        animation: slideDown 0.6s ease-out;
      }

      .header-title {
        font-size: 34px;
        font-weight: 800;
        color: #1a365d;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
      }

      .add-btn {
        width: 35px;
        height: 35px;
        background: linear-gradient(135deg, #58aee5 0%, #4a9fd9 100%);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        box-shadow: 0 4px 15px rgba(88, 174, 229, 0.3);
        border: none;
        color: white;
        font-size: 20px;
        font-weight: bold;
      }

      .add-btn:hover {
        transform: translateY(-2px) scale(1.05);
        box-shadow: 0 8px 25px rgba(88, 174, 229, 0.4);
      }

      /* Content Container */
      .content {
        padding: 0 25px;
        height: calc(100% - 180px);
        overflow-y: auto;
        animation: slideUp 0.6s ease-out 0.2s both;
      }

      /* Today's Pick */
      .todays-pick {
        background: linear-gradient(
          135deg,
          rgba(88, 174, 229, 0.1) 0%,
          rgba(88, 174, 229, 0.05) 100%
        );
        border-radius: 20px;
        padding: 20px;
        margin-bottom: 30px;
        position: relative;
        overflow: hidden;
        box-shadow: 0 8px 25px rgba(88, 174, 229, 0.1);
        animation: slideRight 0.6s ease-out 0.4s both;
      }

      .pick-header {
        display: flex;
        align-items: center;
        gap: 8px;
        margin-bottom: 8px;
      }

      .pick-icon {
        color: #ffd700;
        font-size: 16px;
      }

      .pick-label {
        font-size: 12px;
        font-weight: 700;
        color: #58aee5;
        text-transform: uppercase;
        letter-spacing: 1px;
      }

      .pick-content {
        display: flex;
        justify-content: space-between;
        align-items: center;
      }

      .pick-info {
        flex: 1;
      }

      .pick-title {
        font-size: 16px;
        font-weight: 700;
        color: #1a365d;
        line-height: 1.3;
      }

      .pick-image {
        width: 80px;
        height: 80px;
        border-radius: 15px;
        background: linear-gradient(135deg, #58aee5 0%, #4a9fd9 100%);
        display: flex;
        align-items: center;
        justify-content: center;
        box-shadow: 0 8px 20px rgba(88, 174, 229, 0.3);
        position: relative;
        overflow: hidden;
      }

      .pick-image::before {
        content: "🎵";
        font-size: 32px;
        color: white;
      }

      /* Record Groups */
      .section {
        margin-bottom: 30px;
        animation: slideUp 0.6s ease-out both;
      }

      .section:nth-child(2) {
        animation-delay: 0.6s;
      }
      .section:nth-child(3) {
        animation-delay: 0.8s;
      }
      .section:nth-child(4) {
        animation-delay: 1s;
      }

      .section-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 15px;
      }

      .section-title {
        font-size: 22px;
        font-weight: 700;
        color: #1a365d;
      }

      .section-arrow {
        color: #9ca3af;
        font-size: 18px;
        cursor: pointer;
        transition: all 0.3s ease;
      }

      .section-arrow:hover {
        color: #58aee5;
        transform: translateX(3px);
      }

      /* Record Group Items */
      .record-group {
        background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
        border-radius: 15px;
        padding: 16px;
        margin-bottom: 12px;
        cursor: pointer;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        box-shadow: 0 4px 15px rgba(88, 174, 229, 0.08);
        border: 1px solid rgba(88, 174, 229, 0.05);
        position: relative;
        overflow: hidden;
      }

      .record-group::before {
        content: "";
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(88, 174, 229, 0.05), transparent);
        transition: left 0.5s;
      }

      .record-group:hover::before {
        left: 100%;
      }

      .record-group:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(88, 174, 229, 0.15);
        border-color: rgba(88, 174, 229, 0.1);
      }

      .group-name {
        font-size: 17px;
        font-weight: 700;
        color: #1a365d;
        margin-bottom: 4px;
      }

      .group-songs {
        font-size: 14px;
        color: #718096;
        line-height: 1.4;
      }

      .group-arrow {
        position: absolute;
        right: 16px;
        top: 50%;
        transform: translateY(-50%);
        color: #9ca3af;
        font-size: 16px;
        transition: all 0.3s ease;
      }

      .record-group:hover .group-arrow {
        color: #58aee5;
        transform: translateY(-50%) translateX(3px);
      }

      /* Featured Albums */
      .featured-section {
        margin-bottom: 25px;
      }

      .featured-title {
        font-size: 22px;
        font-weight: 700;
        color: #1a365d;
        margin-bottom: 8px;
      }

      .featured-subtitle {
        font-size: 15px;
        color: #718096;
        margin-bottom: 20px;
        line-height: 1.4;
      }

      .albums-grid {
        display: flex;
        gap: 15px;
        overflow-x: auto;
        padding-bottom: 10px;
      }

      .albums-grid::-webkit-scrollbar {
        display: none;
      }

      .album-card {
        min-width: 140px;
        cursor: pointer;
        transition: all 0.3s ease;
      }

      .album-card:hover {
        transform: translateY(-3px);
      }

      .album-cover {
        width: 140px;
        height: 140px;
        border-radius: 15px;
        background: linear-gradient(135deg, #58aee5 0%, #4a9fd9 100%);
        margin-bottom: 12px;
        box-shadow: 0 8px 25px rgba(88, 174, 229, 0.2);
        display: flex;
        align-items: center;
        justify-content: center;
        position: relative;
        overflow: hidden;
        transition: all 0.3s ease;
      }

      .album-cover:hover {
        box-shadow: 0 15px 35px rgba(88, 174, 229, 0.3);
      }

      .album-cover::before {
        content: "🎼";
        font-size: 40px;
        color: white;
      }

      .album-cover.alt1 {
        background: linear-gradient(135deg, #10b981 0%, #059669 100%);
      }

      .album-cover.alt1::before {
        content: "🎸";
      }

      .album-cover.alt2 {
        background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
      }

      .album-cover.alt2::before {
        content: "🎤";
      }

      .album-title {
        font-size: 16px;
        font-weight: 700;
        color: #1a365d;
        margin-bottom: 4px;
        line-height: 1.3;
      }

      .album-artist {
        font-size: 14px;
        color: #718096;
      }

      @keyframes slideDown {
        from {
          opacity: 0;
          transform: translateY(-30px);
        }
        to {
          opacity: 1;
          transform: translateY(0);
        }
      }

      @keyframes slideUp {
        from {
          opacity: 0;
          transform: translateY(30px);
        }
        to {
          opacity: 1;
          transform: translateY(0);
        }
      }

      @keyframes slideRight {
        from {
          opacity: 0;
          transform: translateX(-30px);
        }
        to {
          opacity: 1;
          transform: translateX(0);
        }
      }
    </style>
  </head>
  <body>
    <div class="phone-container">
      <!-- Status Bar -->
      <div class="status-bar">
        <div class="time">9:41</div>
        <div class="status-icons">
          <span>📶</span>
          <span>📶</span>
          <span>🔋</span>
        </div>
      </div>

      <!-- Header -->
      <div class="header">
        <h1 class="header-title">Records</h1>
        <button class="add-btn">+</button>
      </div>

      <!-- Content -->
      <div class="content">
        <!-- Today's Pick -->
        <div class="todays-pick">
          <div class="pick-header">
            <span class="pick-icon">✨</span>
            <span class="pick-label">Today's Pick</span>
          </div>
          <div class="pick-content">
            <div class="pick-info">
              <div class="pick-title">F-1 Trillion / Post Malone</div>
            </div>
            <div class="pick-image"></div>
          </div>
        </div>

        <!-- Record Groups -->
        <div class="section">
          <div class="section-header">
            <h2 class="section-title">Record Groups</h2>
            <span class="section-arrow">›</span>
          </div>

          <div class="record-group">
            <div class="group-name">Good times</div>
            <div class="group-songs">I Said I Love You First, The Secret of Us, So Clo...</div>
            <div class="group-arrow">›</div>
          </div>

          <div class="record-group">
            <div class="group-name">Feeling kinda...</div>
            <div class="group-songs">Am I Okay?, Don't Mind If I Do, Earcandy</div>
            <div class="group-arrow">›</div>
          </div>

          <div class="record-group">
            <div class="group-name">Back Porch Country</div>
            <div class="group-songs">Whirlwind, What Not To, I'm The Problem</div>
            <div class="group-arrow">›</div>
          </div>
        </div>

        <!-- Featured Albums -->
        <div class="section featured-section">
          <h2 class="featured-title">Country gold, summer soul</h2>
          <p class="featured-subtitle">Laid-back country for warm summer nights</p>

          <div class="albums-grid">
            <div class="album-card">
              <div class="album-cover"></div>
              <div class="album-title">What Not To</div>
              <div class="album-artist">Tucker Wetmore</div>
            </div>
            <div class="album-card">
              <div class="album-cover alt1"></div>
              <div class="album-title">The High Road</div>
              <div class="album-artist">Kane Brown</div>
            </div>
            <div class="album-card">
              <div class="album-cover alt2"></div>
              <div class="album-title">I'm Comin'</div>
              <div class="album-artist">Morgan Wallen</div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <script>
      // Add interactive functionality
      document.querySelectorAll(".record-group").forEach(group => {
        group.addEventListener("click", function () {
          this.style.transform = "translateY(-2px) scale(0.98)";
          setTimeout(() => {
            this.style.transform = "translateY(-2px)";
          }, 150);
        });
      });

      document.querySelectorAll(".album-card").forEach(card => {
        card.addEventListener("click", function () {
          const cover = this.querySelector(".album-cover");
          cover.style.transform = "scale(0.95)";
          setTimeout(() => {
            cover.style.transform = "scale(1)";
          }, 200);
        });
      });

      document.querySelectorAll(".nav-item").forEach(item => {
        item.addEventListener("click", function () {
          document.querySelectorAll(".nav-item").forEach(i => i.classList.remove("active"));
          this.classList.add("active");
        });
      });

      document.querySelector(".add-btn").addEventListener("click", function () {
        this.style.transform = "translateY(-2px) scale(0.9)";
        setTimeout(() => {
          this.style.transform = "translateY(-2px) scale(1.05)";
        }, 150);
      });

      // Add ripple effect to cards
      document.querySelectorAll(".record-group, .album-card").forEach(card => {
        card.addEventListener("click", function (e) {
          const ripple = document.createElement("div");
          const rect = this.getBoundingClientRect();
          const size = Math.max(rect.width, rect.height);
          const x = e.clientX - rect.left - size / 2;
          const y = e.clientY - rect.top - size / 2;

          ripple.style.cssText = `
                    position: absolute;
                    width: ${size}px;
                    height: ${size}px;
                    left: ${x}px;
                    top: ${y}px;
                    background: rgba(88, 174, 229, 0.2);
                    border-radius: 50%;
                    transform: scale(0);
                    animation: ripple 0.6s ease-out;
                    pointer-events: none;
                    z-index: 1;
                `;

          this.appendChild(ripple);
          setTimeout(() => ripple.remove(), 600);
        });
      });

      // Add ripple animation
      const style = document.createElement("style");
      style.textContent = `
            @keyframes ripple {
                to {
                    transform: scale(2);
                    opacity: 0;
                }
            }
        `;
      document.head.appendChild(style);
    </script>
  </body>
</html>
