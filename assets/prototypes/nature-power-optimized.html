<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Nature Power - Optimized</title>
    <style>
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      body {
        font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
        /* background: linear-gradient(135deg, #58aee5 0%, #4a9fd9 50%, #3b8fcc 100%); */
        min-height: 100vh;
        display: flex;
        justify-content: center;
        align-items: center;
        padding: 20px;
      }

      .phone-container {
        width: 375px;
        height: 812px;
        /* background: linear-gradient(180deg, rgba(88, 174, 229, 0.1) 0%, rgba(88, 174, 229, 0.05) 100%); */
        background: linear-gradient(135deg, #58aee5 0%, #4a9fd9 50%, #3b8fcc 100%);
        border-radius: 40px;
        padding: 20px;
        position: relative;
        box-shadow: 0 25px 50px rgba(88, 174, 229, 0.3);
        backdrop-filter: blur(20px);
        border: 1px solid rgba(255, 255, 255, 0.2);
        overflow: hidden;
      }

      .header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 30px;
        margin-top: 20px;
        animation: slideDown 0.6s ease-out;
      }

      .greeting {
        font-size: 24px;
        font-weight: 700;
        color: #1a365d;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      }

      .weather-widget {
        background: rgba(255, 255, 255, 0.95);
        border-radius: 20px;
        padding: 10px 16px;
        display: flex;
        align-items: center;
        gap: 10px;
        backdrop-filter: blur(20px);
        box-shadow: 0 8px 25px rgba(88, 174, 229, 0.2);
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      }

      .weather-widget:hover {
        transform: translateY(-2px);
        box-shadow: 0 12px 35px rgba(88, 174, 229, 0.3);
      }

      .weather-icon {
        width: 28px;
        height: 28px;
        background: linear-gradient(135deg, #ffd700 0%, #ffed4e 100%);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        box-shadow: 0 4px 12px rgba(255, 215, 0, 0.3);
      }

      .weather-icon::before {
        content: "☀️";
        font-size: 16px;
      }

      .weather-text {
        font-size: 14px;
        font-weight: 600;
        color: #1a365d;
        line-height: 1.2;
      }

      .country-section {
        display: flex;
        align-items: center;
        gap: 10px;
        margin-bottom: 15px;
        animation: slideRight 0.6s ease-out 0.2s both;
      }

      .flag {
        width: 24px;
        height: 16px;
        background: linear-gradient(to bottom, #ef4444 0%, #ef4444 33%, #ffffff 33%, #ffffff 66%, #3b82f6 66%);
        border-radius: 3px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
      }

      .country-name {
        font-size: 14px;
        font-weight: 700;
        color: #4a5568;
        letter-spacing: 1.5px;
      }

      .main-title {
        font-size: 52px;
        font-weight: 900;
        color: #1a365d;
        line-height: 1.1;
        margin-bottom: 30px;
        text-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        animation: slideUp 0.6s ease-out 0.4s both;
      }

      .search-icon {
        position: absolute;
        right: 30px;
        top: 200px;
        width: 55px;
        height: 110px;
        background: rgba(255, 255, 255, 0.95);
        border-radius: 30px;
        display: flex;
        align-items: center;
        justify-content: center;
        backdrop-filter: blur(20px);
        box-shadow: 0 10px 30px rgba(88, 174, 229, 0.2);
        cursor: pointer;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        animation: slideLeft 0.6s ease-out 0.6s both;
      }

      .search-icon:hover {
        transform: translateY(-3px) scale(1.05);
        box-shadow: 0 15px 40px rgba(88, 174, 229, 0.3);
      }

      .search-icon::before {
        content: "🔍";
        font-size: 22px;
      }

      .activity-buttons {
        display: flex;
        gap: 12px;
        margin-bottom: 30px;
        animation: slideUp 0.6s ease-out 0.8s both;
      }

      .activity-btn {
        padding: 14px 22px;
        border-radius: 30px;
        border: none;
        font-size: 16px;
        font-weight: 600;
        cursor: pointer;
        display: flex;
        align-items: center;
        gap: 8px;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        position: relative;
        overflow: hidden;
      }

      .activity-btn::before {
        content: "";
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
        transition: left 0.5s;
      }

      .activity-btn:hover::before {
        left: 100%;
      }

      .activity-btn.active {
        background: linear-gradient(135deg, #58aee5 0%, #4a9fd9 100%);
        color: white;
        box-shadow: 0 8px 25px rgba(88, 174, 229, 0.4);
      }

      .activity-btn:not(.active) {
        background: rgba(255, 255, 255, 0.9);
        color: #4a5568;
        box-shadow: 0 4px 15px rgba(88, 174, 229, 0.1);
      }

      .activity-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 12px 35px rgba(88, 174, 229, 0.3);
      }

      .nature-card {
        /* background: linear-gradient(180deg, rgba(0, 0, 0, 0.4) 0%, rgba(0, 0, 0, 0.7) 100%), linear-gradient(135deg, #58aee5 0%, #2d5a87 100%); */
        /* border-radius: 30px; */
        padding: 35px;
        color: white;
        text-align: center;
        height: 320px;
        display: flex;
        flex-direction: column;
        justify-content: flex-end;
        position: relative;
        margin-bottom: 30px;
        /* box-shadow: 0 20px 40px rgba(88, 174, 229, 0.3); */
        animation: slideUp 0.6s ease-out 1s both;
        overflow: hidden;
      }

      .nature-card::before {
        /* content: "";
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0; */
        /* background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 400 300"><rect fill="%23228B22" width="400" height="300"/><polygon fill="%23006400" points="50,300 80,200 110,300"/><polygon fill="%23006400" points="150,300 180,180 210,300"/><polygon fill="%23006400" points="250,300 280,190 310,300"/><polygon fill="%23006400" points="350,300 380,170 400,300"/></svg>'); */
        /* background-size: cover;
        opacity: 0.3;
        z-index: 1; */
      }

      .nature-card > * {
        position: relative;
        z-index: 2;
      }

      .card-title {
        font-size: 32px;
        font-weight: 800;
        margin-bottom: 18px;
        text-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
      }

      .card-description {
        font-size: 17px;
        line-height: 1.5;
        margin-bottom: 28px;
        opacity: 0.95;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
      }

      .trip-details {
        display: flex;
        justify-content: center;
        gap: 25px;
        margin-bottom: 28px;
      }

      .trip-detail {
        display: flex;
        align-items: center;
        gap: 6px;
        font-size: 15px;
        font-weight: 600;
        background: rgba(255, 255, 255, 0.1);
        padding: 8px 12px;
        border-radius: 20px;
        backdrop-filter: blur(10px);
      }

      .bottom-nav {
        position: absolute;
        bottom: 30px;
        left: 50%;
        transform: translateX(-50%);
        background: linear-gradient(135deg, rgba(26, 54, 93, 0.95) 0%, rgba(45, 90, 135, 0.95) 100%);
        border-radius: 25px;
        padding: 15px 25px;
        display: flex;
        justify-content: space-around;
        align-items: center;
        backdrop-filter: blur(20px);
        -webkit-backdrop-filter: blur(20px);
        box-shadow: 0 10px 30px rgba(26, 54, 93, 0.3);
        border: 1px solid rgba(255, 255, 255, 0.1);
        width: calc(100% - 50px);
        max-width: 300px;
      }

      .nav-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 4px;
        cursor: pointer;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        padding: 8px 12px;
        border-radius: 12px;
        flex: 1;
      }

      .nav-icon {
        width: 28px;
        height: 28px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 18px;
        transition: all 0.3s ease;
      }

      .nav-label {
        font-size: 11px;
        font-weight: 500;
        transition: all 0.3s ease;
      }

      .nav-item.active {
        background: rgba(88, 174, 229, 0.2);
      }

      .nav-item.active .nav-icon {
        color: #58aee5;
        transform: scale(1.1);
      }

      .nav-item.active .nav-label {
        color: #58aee5;
        font-weight: 600;
      }

      .nav-item:not(.active) .nav-icon {
        color: #9ca3af;
      }

      .nav-item:not(.active) .nav-label {
        color: #9ca3af;
      }

      .nav-item:hover {
        transform: translateY(-2px);
      }

      @keyframes slideDown {
        from {
          opacity: 0;
          transform: translateY(-30px);
        }
        to {
          opacity: 1;
          transform: translateY(0);
        }
      }

      @keyframes slideUp {
        from {
          opacity: 0;
          transform: translateY(30px);
        }
        to {
          opacity: 1;
          transform: translateY(0);
        }
      }

      @keyframes slideRight {
        from {
          opacity: 0;
          transform: translateX(-30px);
        }
        to {
          opacity: 1;
          transform: translateX(0);
        }
      }

      @keyframes slideLeft {
        from {
          opacity: 0;
          transform: translateX(30px);
        }
        to {
          opacity: 1;
          transform: translateX(0);
        }
      }
    </style>
  </head>
  <body>
    <div class="phone-container">
      <div class="header">
        <div class="greeting">Hi, Morgan 👋</div>
        <div class="weather-widget">
          <div class="weather-icon"></div>
          <div class="weather-text">
            Weather
            <br />
            15°C
          </div>
        </div>
      </div>

      <!-- <div class="search-icon"></div> -->

      <div class="country-section">
        <div class="flag"></div>
        <div class="country-name">NORWAY</div>
      </div>

      <h1 class="main-title">
        Nature
        <br />
        Power
      </h1>

      <div class="activity-buttons">
        <button class="activity-btn active">🥾 Hiking</button>
        <button class="activity-btn">🛶 Kayaking</button>
        <button class="activity-btn">🚴 Biking</button>
      </div>

      <div class="nature-card">
        <h2 class="card-title">The Sounds of Nature</h2>
        <p class="card-description">A real adventure where nature reveals its grandeur and beauty in its purest form.</p>
        <div class="trip-details">
          <div class="trip-detail">📅 7 days</div>
          <div class="trip-detail">📍 10 km</div>
          <div class="trip-detail">⭐ 8/10</div>
        </div>
      </div>

      <div class="bottom-nav">
        <div class="nav-item active">
          <div class="nav-icon">💳</div>
          <div class="nav-label">Wallet</div>
        </div>
        <div class="nav-item">
          <div class="nav-icon">📊</div>
          <div class="nav-label">Tracking</div>
        </div>
        <div class="nav-item">
          <div class="nav-icon">⚡</div>
          <div class="nav-label">Apps</div>
        </div>
        <div class="nav-item">
          <div class="nav-icon">⚙️</div>
          <div class="nav-label">Settings</div>
        </div>
      </div>
    </div>
  </body>
</html>
