<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Sleep Product - Optimized</title>
    <style>
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      body {
        font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
        background: linear-gradient(135deg, #58aee5 0%, #4a9fd9 50%, #3b8fcc 100%);
        min-height: 100vh;
        display: flex;
        justify-content: center;
        align-items: center;
        padding: 20px;
      }

      .phone-container {
        width: 375px;
        height: 812px;
        background: linear-gradient(180deg, #58aee5 0%, #4a9fd9 100%);
        border-radius: 40px;
        position: relative;
        box-shadow: 0 25px 50px rgba(88, 174, 229, 0.4);
        overflow: hidden;
        border: 1px solid rgba(255, 255, 255, 0.2);
      }

      .header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 20px 25px;
        margin-top: 20px;
        animation: slideDown 0.6s ease-out;
      }

      .back-btn {
        width: 45px;
        height: 45px;
        background: rgba(255, 255, 255, 0.15);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        backdrop-filter: blur(20px);
      }

      .back-btn::before {
        content: "←";
        color: white;
        font-size: 22px;
        font-weight: bold;
      }

      .back-btn:hover {
        background: rgba(255, 255, 255, 0.25);
        transform: translateY(-2px) scale(1.05);
        box-shadow: 0 8px 25px rgba(255, 255, 255, 0.2);
      }

      .logo {
        color: white;
        font-size: 26px;
        font-weight: 300;
        letter-spacing: 3px;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      }

      .header-icons {
        display: flex;
        gap: 15px;
      }

      .header-icon {
        width: 45px;
        height: 45px;
        background: rgba(255, 255, 255, 0.15);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        backdrop-filter: blur(20px);
      }

      .header-icon:hover {
        background: rgba(255, 255, 255, 0.25);
        transform: translateY(-2px) scale(1.05);
        box-shadow: 0 8px 25px rgba(255, 255, 255, 0.2);
      }

      .header-icon::before {
        color: white;
        font-size: 18px;
      }

      .settings-icon::before {
        content: "⚙️";
      }
      .cart-icon::before {
        content: "🛒";
      }

      .product-nav {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0 25px;
        margin-bottom: 30px;
        animation: slideRight 0.6s ease-out 0.2s both;
      }

      .nav-tabs {
        display: flex;
        gap: 35px;
      }

      .nav-tab {
        display: flex;
        align-items: center;
        gap: 10px;
        color: rgba(255, 255, 255, 0.6);
        font-size: 17px;
        cursor: pointer;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        padding: 8px 12px;
        border-radius: 20px;
        position: relative;
        overflow: hidden;
      }

      .nav-tab::before {
        content: "";
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
        transition: left 0.5s;
      }

      .nav-tab:hover::before {
        left: 100%;
      }

      .nav-tab.active {
        color: white;
        font-weight: 700;
        background: rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(10px);
      }

      .nav-tab:first-child::after {
        content: "😌";
      }
      .nav-tab:last-child::after {
        content: "😴";
      }

      .quantity-options {
        display: flex;
        flex-direction: column;
        gap: 10px;
      }

      .quantity-option {
        width: 40px;
        height: 40px;
        background: rgba(255, 255, 255, 0.1);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: rgba(255, 255, 255, 0.7);
        font-size: 15px;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        backdrop-filter: blur(10px);
      }

      .quantity-option:first-child {
        background: rgba(255, 255, 255, 0.25);
        color: white;
        font-weight: 700;
        box-shadow: 0 4px 15px rgba(255, 255, 255, 0.2);
      }

      .quantity-option:hover {
        background: rgba(255, 255, 255, 0.25);
        color: white;
        transform: scale(1.1);
      }

      .product-image-container {
        display: flex;
        justify-content: center;
        align-items: center;
        padding: 20px 40px;
        margin-bottom: 30px;
        animation: scaleIn 0.8s ease-out 0.4s both;
        position: relative;
        height: 320px;
      }

      .image-carousel {
        position: relative;
        width: 100%;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
      }

      .carousel-container {
        position: relative;
        width: 280px;
        height: 280px;
        display: flex;
        align-items: center;
        justify-content: center;
      }

      .product-image {
        position: absolute;
        border-radius: 25px;
        background: linear-gradient(145deg, #58aee5 0%, #4a9fd9 100%);
        transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
        cursor: pointer;
        box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2);
      }

      .product-image.main {
        width: 200px;
        height: 280px;
        z-index: 3;
        transform: scale(1);
      }

      .product-image.side {
        width: 160px;
        height: 220px;
        z-index: 2;
        opacity: 0.7;
      }

      .product-image.side.left {
        left: -80px;
        transform: rotateY(-15deg);
      }

      .product-image.side.right {
        right: -80px;
        transform: rotateY(15deg);
      }

      .product-image:hover {
        transform: translateY(-5px) scale(1.02);
        box-shadow: 0 25px 50px rgba(0, 0, 0, 0.3);
      }

      .carousel-nav {
        position: absolute;
        top: 50%;
        transform: translateY(-50%);
        width: 45px;
        height: 45px;
        background: rgba(255, 255, 255, 0.9);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        transition: all 0.3s ease;
        backdrop-filter: blur(20px);
        z-index: 4;
      }

      .carousel-nav:hover {
        background: white;
        transform: translateY(-50%) scale(1.1);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
      }

      .carousel-nav.prev {
        left: -20px;
      }

      .carousel-nav.next {
        right: -20px;
      }

      .carousel-nav::before {
        font-size: 20px;
        font-weight: bold;
        color: #58aee5;
      }

      .carousel-nav.prev::before {
        content: "‹";
      }

      .carousel-nav.next::before {
        content: "›";
      }

      .carousel-indicators {
        position: absolute;
        bottom: -30px;
        left: 50%;
        transform: translateX(-50%);
        display: flex;
        gap: 8px;
        z-index: 4;
      }

      .indicator {
        width: 8px;
        height: 8px;
        border-radius: 50%;
        background: rgba(255, 255, 255, 0.4);
        cursor: pointer;
        transition: all 0.3s ease;
      }

      .indicator.active {
        background: white;
        width: 20px;
        border-radius: 10px;
      }

      .product-details {
        background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
        border-radius: 35px 35px 0 0;
        padding: 30px 25px;
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        height: 350px;
        box-shadow: 0 -10px 40px rgba(88, 174, 229, 0.1);
        animation: slideUp 0.6s ease-out 0.6s both;
        overflow-y: auto;
      }

      .product-title {
        font-size: 32px;
        font-weight: 800;
        color: #1a365d;
        margin-bottom: 20px;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
        line-height: 1.2;
      }

      .time-location-section {
        display: flex;
        align-items: center;
        gap: 20px;
        margin-bottom: 20px;
        padding: 15px 0;
      }

      .time-info {
        display: flex;
        align-items: center;
        gap: 8px;
        background: rgba(88, 174, 229, 0.1);
        padding: 8px 16px;
        border-radius: 20px;
        border: 1px solid rgba(88, 174, 229, 0.2);
      }

      .time-icon {
        font-size: 16px;
      }

      .time-text {
        font-size: 14px;
        font-weight: 600;
        color: #58aee5;
      }

      .location-info {
        display: flex;
        align-items: center;
        gap: 8px;
        background: rgba(16, 185, 129, 0.1);
        padding: 8px 16px;
        border-radius: 20px;
        border: 1px solid rgba(16, 185, 129, 0.2);
      }

      .location-icon {
        font-size: 16px;
      }

      .location-text {
        font-size: 14px;
        font-weight: 600;
        color: #10b981;
      }

      .tags-section {
        margin-bottom: 25px;
      }

      .tags-title {
        font-size: 16px;
        font-weight: 600;
        color: #4a5568;
        margin-bottom: 12px;
      }

      .tags-container {
        display: flex;
        flex-wrap: wrap;
        gap: 8px;
      }

      .tag {
        background: linear-gradient(135deg, #58aee5 0%, #4a9fd9 100%);
        color: white;
        padding: 6px 14px;
        border-radius: 18px;
        font-size: 12px;
        font-weight: 600;
        box-shadow: 0 2px 8px rgba(88, 174, 229, 0.2);
        transition: all 0.3s ease;
      }

      .tag:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(88, 174, 229, 0.3);
      }

      .tag.secondary {
        background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
        box-shadow: 0 2px 8px rgba(245, 158, 11, 0.2);
      }

      .tag.secondary:hover {
        box-shadow: 0 4px 12px rgba(245, 158, 11, 0.3);
      }

      .tag.tertiary {
        background: linear-gradient(135deg, #10b981 0%, #059669 100%);
        box-shadow: 0 2px 8px rgba(16, 185, 129, 0.2);
      }

      .tag.tertiary:hover {
        box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
      }

      .story-section {
        margin-bottom: 25px;
      }

      .story-title {
        font-size: 18px;
        font-weight: 700;
        color: #1a365d;
        margin-bottom: 15px;
        display: flex;
        align-items: center;
        gap: 8px;
      }

      .story-title::before {
        content: "📖";
        font-size: 20px;
      }

      .story-content {
        background: linear-gradient(135deg, #f8fafc 0%, #ffffff 100%);
        border-radius: 20px;
        padding: 20px;
        border: 1px solid rgba(88, 174, 229, 0.1);
        box-shadow: 0 4px 15px rgba(88, 174, 229, 0.05);
        position: relative;
        overflow: hidden;
      }

      .story-content::before {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        width: 4px;
        height: 100%;
        background: linear-gradient(135deg, #58aee5 0%, #4a9fd9 100%);
      }

      .story-text {
        font-size: 15px;
        line-height: 1.6;
        color: #4a5568;
        text-align: justify;
        position: relative;
        z-index: 1;
      }

      .story-text::first-letter {
        font-size: 48px;
        font-weight: 700;
        color: #58aee5;
        float: left;
        line-height: 40px;
        margin: 0 8px 0 0;
        font-family: Georgia, serif;
      }

      .price {
        font-size: 32px;
        font-weight: 800;
        color: #1a365d;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
      }

      .quantity-selector {
        display: flex;
        align-items: center;
        gap: 18px;
      }

      .quantity-btn {
        width: 45px;
        height: 45px;
        background: linear-gradient(135deg, #58aee5 0%, #4a9fd9 100%);
        border: none;
        border-radius: 50%;
        color: white;
        font-size: 22px;
        font-weight: bold;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        box-shadow: 0 6px 20px rgba(88, 174, 229, 0.3);
      }

      .quantity-btn:hover {
        background: linear-gradient(135deg, #4a9fd9 0%, #3b8fcc 100%);
        transform: translateY(-2px) scale(1.05);
        box-shadow: 0 10px 30px rgba(88, 174, 229, 0.4);
      }

      .quantity-display {
        font-size: 20px;
        font-weight: 700;
        color: #1a365d;
        min-width: 25px;
        text-align: center;
      }

      @keyframes slideDown {
        from {
          opacity: 0;
          transform: translateY(-30px);
        }
        to {
          opacity: 1;
          transform: translateY(0);
        }
      }

      @keyframes slideUp {
        from {
          opacity: 0;
          transform: translateY(30px);
        }
        to {
          opacity: 1;
          transform: translateY(0);
        }
      }

      @keyframes slideRight {
        from {
          opacity: 0;
          transform: translateX(-30px);
        }
        to {
          opacity: 1;
          transform: translateX(0);
        }
      }

      @keyframes scaleIn {
        from {
          opacity: 0;
          transform: scale(0.8);
        }
        to {
          opacity: 1;
          transform: scale(1);
        }
      }

      .product-label {
        position: absolute;
        top: 10px;
        left: 10px;
        background: rgba(255, 255, 255, 0.8);
        border-radius: 10px;
        padding: 8px;
        text-align: left;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
      }

      .product-label-icon {
        font-size: 16px;
        margin-right: 5px;
      }

      .product-label-text {
        font-size: 14px;
        font-weight: bold;
        color: #333;
      }

      .product-label-subtext {
        font-size: 10px;
        color: #666;
      }

      /* Custom scrollbar for product details */
      .product-details::-webkit-scrollbar {
        width: 4px;
      }

      .product-details::-webkit-scrollbar-track {
        background: rgba(88, 174, 229, 0.1);
        border-radius: 2px;
      }

      .product-details::-webkit-scrollbar-thumb {
        background: linear-gradient(135deg, #58aee5 0%, #4a9fd9 100%);
        border-radius: 2px;
      }

      .product-details::-webkit-scrollbar-thumb:hover {
        background: linear-gradient(135deg, #4a9fd9 0%, #3b8fcc 100%);
      }
    </style>
  </head>
  <body>
    <div class="phone-container">
      <div class="header">
        <div class="back-btn"></div>
        <div class="logo">xefag</div>
        <div class="header-icons">
          <div class="header-icon settings-icon"></div>
          <div class="header-icon cart-icon"></div>
        </div>
      </div>

      <!-- <div class="product-nav">
        <div class="nav-tabs">
          <div class="nav-tab">Relax</div>
          <div class="nav-tab active">Sleep</div>
        </div>
        <div class="quantity-options">
          <div class="quantity-option">30</div>
          <div class="quantity-option">60</div>
          <div class="quantity-option">90</div>
        </div>
      </div> -->

      <div class="product-image-container">
        <div class="image-carousel">
          <div class="carousel-container">
            <div class="product-image main" data-index="0">
              <div class="product-label">
                <div class="product-label-icon">😴</div>
                <div class="product-label-text">Sleep</div>
                <div class="product-label-subtext">by xefag</div>
              </div>
            </div>
            <div class="product-image side left" data-index="1">
              <div class="product-label">
                <div class="product-label-icon">😌</div>
                <div class="product-label-text">Relax</div>
                <div class="product-label-subtext">by xefag</div>
              </div>
            </div>
            <div class="product-image side right" data-index="2">
              <div class="product-label">
                <div class="product-label-icon">🧠</div>
                <div class="product-label-text">Focus</div>
                <div class="product-label-subtext">by xefag</div>
              </div>
            </div>
          </div>
          <div class="carousel-nav prev"></div>
          <div class="carousel-nav next"></div>
          <div class="carousel-indicators">
            <div class="indicator active"></div>
            <div class="indicator"></div>
            <div class="indicator"></div>
          </div>
        </div>
      </div>

      <div class="product-details">
        <h1 class="product-title">Sleep 30 Dissolvable Wafers</h1>

        <div class="time-location-section">
          <div class="time-info">
            <span class="time-icon">⏰</span>
            <span class="time-text">2 years ago</span>
          </div>
          <div class="location-info">
            <span class="location-icon">📍</span>
            <span class="location-text">Oslo, Norway</span>
          </div>
        </div>

        <div class="tags-section">
          <h3 class="tags-title">Product Tags</h3>
          <div class="tags-container">
            <span class="tag">Sleep Aid</span>
            <span class="tag secondary">Natural</span>
            <span class="tag tertiary">Fast Acting</span>
            <span class="tag">Melatonin</span>
            <span class="tag secondary">Herbal</span>
            <span class="tag tertiary">Non-Habit Forming</span>
            <span class="tag">Dissolvable</span>
            <span class="tag secondary">Mint Flavor</span>
            <span class="tag tertiary">Vegan</span>
            <span class="tag">Gluten Free</span>
          </div>
        </div>

        <div class="story-section">
          <h3 class="story-title">My Sleep Journey</h3>
          <div class="story-content">
            <p class="story-text">
              After years of struggling with insomnia and restless nights, I discovered these amazing sleep wafers during a particularly stressful period in my life. Working late nights as a software developer in Oslo, I found myself tossing and turning, unable to quiet my racing mind. These dissolvable wafers became my nightly ritual. The gentle mint flavor is soothing, and within 20 minutes, I feel a natural drowsiness washing over me. Unlike other sleep aids I've tried, these don't leave me
              groggy in the morning. Instead, I wake up refreshed and ready to tackle the day. What I love most is how they've helped me establish a consistent sleep schedule. The natural ingredients give me peace of mind, knowing I'm not putting harsh chemicals into my body. After two years of regular use, they've truly transformed my relationship with sleep.
            </p>
          </div>
        </div>
      </div>
    </div>

    <script>
      const minusBtn = document.querySelector(".quantity-btn:first-child");
      const plusBtn = document.querySelector(".quantity-btn:last-child");
      const quantityDisplay = document.querySelector(".quantity-display");

      let quantity = 1;

      minusBtn.addEventListener("click", () => {
        if (quantity > 1) {
          quantity--;
          quantityDisplay.textContent = quantity;
          quantityDisplay.style.transform = "scale(1.2)";
          setTimeout(() => (quantityDisplay.style.transform = "scale(1)"), 200);
        }
      });

      plusBtn.addEventListener("click", () => {
        quantity++;
        quantityDisplay.textContent = quantity;
        quantityDisplay.style.transform = "scale(1.2)";
        setTimeout(() => (quantityDisplay.style.transform = "scale(1)"), 200);
      });

      const quantityOptions = document.querySelectorAll(".quantity-option");
      quantityOptions.forEach(option => {
        option.addEventListener("click", () => {
          quantityOptions.forEach(opt => {
            opt.style.background = "rgba(255, 255, 255, 0.1)";
            opt.style.color = "rgba(255, 255, 255, 0.7)";
            opt.style.boxShadow = "none";
          });
          option.style.background = "rgba(255, 255, 255, 0.25)";
          option.style.color = "white";
          option.style.boxShadow = "0 4px 15px rgba(255, 255, 255, 0.2)";
        });
      });

      const navTabs = document.querySelectorAll(".nav-tab");
      navTabs.forEach(tab => {
        tab.addEventListener("click", () => {
          navTabs.forEach(t => {
            t.classList.remove("active");
            t.style.background = "transparent";
          });
          tab.classList.add("active");
          tab.style.background = "rgba(255, 255, 255, 0.1)";
        });
      });

      // Enhanced carousel functionality
      let currentImageIndex = 0;
      const images = document.querySelectorAll(".product-image");
      const indicators = document.querySelectorAll(".indicator");
      const prevBtn = document.querySelector(".carousel-nav.prev");
      const nextBtn = document.querySelector(".carousel-nav.next");

      const imageData = [
        { name: "Sleep", subtext: "by xefag", icon: "😴" },
        { name: "Relax", subtext: "by xefag", icon: "😌" },
        { name: "Focus", subtext: "by xefag", icon: "🧠" },
      ];

      function updateCarousel(newIndex) {
        currentImageIndex = newIndex;

        images.forEach((img, index) => {
          img.classList.remove("main", "side", "left", "right");

          if (index === currentImageIndex) {
            img.classList.add("main");
          } else if (index === (currentImageIndex - 1 + images.length) % images.length) {
            img.classList.add("side", "left");
          } else if (index === (currentImageIndex + 1) % images.length) {
            img.classList.add("side", "right");
          } else {
            img.style.opacity = "0";
            img.style.transform = "scale(0.8)";
          }

          const label = img.querySelector(".product-label-text");
          const subtext = img.querySelector(".product-label-subtext");
          const icon = img.querySelector(".product-label-icon");

          if (label) label.textContent = imageData[index].name;
          if (subtext) subtext.textContent = imageData[index].subtext;
          if (icon) icon.textContent = imageData[index].icon;
        });

        indicators.forEach((indicator, index) => {
          indicator.classList.toggle("active", index === currentImageIndex);
        });
      }

      prevBtn.addEventListener("click", () => {
        const newIndex = (currentImageIndex - 1 + images.length) % images.length;
        updateCarousel(newIndex);
      });

      nextBtn.addEventListener("click", () => {
        const newIndex = (currentImageIndex + 1) % images.length;
        updateCarousel(newIndex);
      });

      indicators.forEach((indicator, index) => {
        indicator.addEventListener("click", () => {
          updateCarousel(index);
        });
      });

      images.forEach((img, index) => {
        img.addEventListener("click", () => {
          if (!img.classList.contains("main")) {
            updateCarousel(index);
          }
        });
      });

      updateCarousel(0);
    </script>
  </body>
</html>
